from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.dao.coupon import coupon_dao, coupon_usage_record_dao, coupon_batch_dao
from app.dao.product import product_dao
from app.schemas.coupon import CouponUsageRecordCreate, ProductQuantityItem
from app.models.coupon import (
    CouponUsageStatus, CouponUsageCycle, CouponScope, CouponType,
    DiscountCoupon, CashCoupon, FullReductionCoupon, DistributionChannel
)
from app.models.order import Order
from app.models.user import User
from app.models.enum import Status
from app.utils.logger import logger
from app.schemas.order import OrderItemBase


class CouponService:
    """优惠券服务类
    
    提供优惠券相关的业务逻辑处理，包括获取可用优惠券、使用优惠券等操作
    """

    @staticmethod
    def get_user_available_coupons(session: Session, order: Order, user: User) -> Dict[str, List[Dict[str, Any]]]:
        """获取用户的优惠券列表
        
        Args:
            session: 数据库会话
            order: 订单对象
            user: 用户对象
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 包含三个优惠券列表的字典：
            - available: 有效可用的优惠券列表
            - valid_but_unavailable: 有效但当前不可用的优惠券列表
            - used: 已经使用的优惠券列表
        """
        try:
            # 1. 获取该用户所有状态为有效且在有效期内的优惠券使用记录
            valid_records = CouponService._get_valid_coupon_records(session, user.id)
            
            # 2. 获取所有该用户状态为已使用的优惠券使用记录
            used_records = CouponService._get_used_coupon_records(session, user.id)
            
            # 3. 初始化三个列表
            available_coupons = []  # 有效可用的优惠券
            valid_but_unavailable_coupons = []  # 有效但不可用的优惠券
            used_coupons = []  # 已使用的优惠券
            
            # 4. 处理有效记录，区分可用和不可用
            for record in valid_records:
                coupon_data = CouponService._format_coupon_data(session, record)
                
                # 检查是否满足所有条件
                is_available = (
                    CouponService._check_order_amount_condition(record.coupon, order) and
                    CouponService._check_product_combination_condition(record.coupon, order) and
                    CouponService._check_usage_limit_condition(record, used_records)
                )
                
                if is_available:
                    available_coupons.append(coupon_data)
                else:
                    # 添加不可用原因
                    reasons = CouponService._get_unavailable_reasons(record.coupon, order, record, used_records)
                    coupon_data["unavailable_reasons"] = reasons
                    valid_but_unavailable_coupons.append(coupon_data)
            
            # 5. 处理已使用的记录
            for record in used_records:
                coupon_data = CouponService._format_coupon_data(session, record)
                used_coupons.append(coupon_data)
            
            result = {
                "available": available_coupons,
                "valid_but_unavailable": valid_but_unavailable_coupons,
                "used": used_coupons
            }
            
            logger.info(f"用户 {user.id} 针对订单 {order.id} 获取优惠券统计: "
                       f"可用 {len(available_coupons)} 张, "
                       f"有效但不可用 {len(valid_but_unavailable_coupons)} 张, "
                       f"已使用 {len(used_coupons)} 张")
            
            return result
            
        except Exception as e:
            logger.error(f"获取优惠券列表失败: {str(e)}")
            raise e

    @staticmethod
    def _get_valid_coupon_records(session: Session, user_id: int) -> List:
        """获取用户所有状态为有效且在有效期内的优惠券使用记录"""
        from app.models.coupon import CouponBatch
        current_time = datetime.now()

        # 查询有效的优惠券使用记录，并关联优惠券信息和批次信息
        # 使用 with_polymorphic 确保正确加载多态对象
        records = session.query(coupon_usage_record_dao.model).join(
            coupon_dao.model, coupon_usage_record_dao.model.coupon_id == coupon_dao.model.id
        ).join(
            CouponBatch, coupon_usage_record_dao.model.coupon_batch_id == CouponBatch.id
        ).filter(
            and_(
                coupon_usage_record_dao.model.user_id == user_id,
                coupon_usage_record_dao.model.status == CouponUsageStatus.VALID,
                CouponBatch.start_time <= current_time,
                CouponBatch.end_time >= current_time
            )
        ).all()

        return records

    @staticmethod
    def _get_used_coupon_records(session: Session, user_id: int) -> List:
        """获取用户所有状态为已使用的优惠券使用记录"""
        return session.query(coupon_usage_record_dao.model).filter(
            and_(
                coupon_usage_record_dao.model.user_id == user_id,
                coupon_usage_record_dao.model.status == CouponUsageStatus.USED
            )
        ).all()

    @staticmethod
    def _check_order_amount_condition(coupon, order: Order) -> bool:
        """检查订单金额条件
        
        Args:
            coupon: 优惠券对象
            order: 订单对象
            
        Returns:
            bool: 是否满足条件
        """
        # 如果优惠券的条件范围是订单
        if coupon.condition_scope == CouponScope.ORDER:
            # 检查订单总金额是否大于条件金额
            return order.total_amount > coupon.condition_amount
        
        # 如果条件范围是商品，这里暂时返回True，具体逻辑在产品组合条件中处理
        return True

    @staticmethod
    def _check_product_combination_condition(coupon, order: Order) -> bool:
        """检查产品组合条件
        
        Args:
            coupon: 优惠券对象
            order: 订单对象
            
        Returns:
            bool: 是否满足条件
        """
        # 如果condition_objects为空数组，不做检查
        if not coupon.condition_objects:
            return True
        
        # 获取订单中的产品ID和数量组合
        order_products = {}
        for item in order.items:
            order_products[item.product_id] = order_products.get(item.product_id, 0) + item.quantity
        
        # 检查condition_objects中的每个条件
        # condition_objects格式: [{"1": 13}, {"5": 2}]
        for condition_item in coupon.condition_objects:
            for product_id_str, required_quantity in condition_item.items():
                product_id = int(product_id_str)
                actual_quantity = order_products.get(product_id, 0)
                
                # 如果订单中该产品的数量小于要求的数量，条件不满足
                if actual_quantity < required_quantity:
                    return False
        
        return True

    @staticmethod
    def _check_usage_limit_condition(record, used_records: List) -> bool:
        """检查使用限制条件
        
        Args:
            session: 数据库会话
            record: 当前优惠券使用记录
            used_records: 已使用的优惠券记录列表
            
        Returns:
            bool: 是否满足条件
        """
        coupon = record.coupon
        usage_cycle = coupon.usage_cycle
        usage_limit = coupon.usage_limit
        
        # 如果没有使用限制，直接返回True
        if usage_limit <= 0:
            return True
        
        # 根据usage_cycle计算时间范围
        current_time = datetime.now()
        start_time = CouponService._get_cycle_start_time(current_time, usage_cycle)
        
        # 统计在指定周期内该优惠券的使用次数
        usage_count = 0
        for used_record in used_records:
            if (used_record.coupon_id == record.coupon_id and 
                used_record.used_at and 
                used_record.used_at >= start_time and 
                used_record.used_at <= current_time):
                usage_count += 1
        
        # 检查是否超过使用限制
        return usage_count < usage_limit

    @staticmethod
    def _get_cycle_start_time(current_time: datetime, usage_cycle: CouponUsageCycle) -> datetime:
        """根据使用周期获取周期开始时间
        
        Args:
            current_time: 当前时间
            usage_cycle: 使用周期
            
        Returns:
            datetime: 周期开始时间
        """
        if usage_cycle == CouponUsageCycle.PER_DAY:
            # 当日00:00:00
            return current_time.replace(hour=0, minute=0, second=0, microsecond=0)
        elif usage_cycle == CouponUsageCycle.PER_WEEK:
            # 本周一00:00:00
            days_since_monday = current_time.weekday()
            monday = current_time - timedelta(days=days_since_monday)
            return monday.replace(hour=0, minute=0, second=0, microsecond=0)
        elif usage_cycle == CouponUsageCycle.PER_MONTH:
            # 本月1日00:00:00
            return current_time.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif usage_cycle == CouponUsageCycle.PER_YEAR:
            # 今年1月1日00:00:00
            return current_time.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        elif usage_cycle == CouponUsageCycle.PER_ORDER:
            # 每次订单不受时间限制，这里返回一个很早的时间
            return datetime(1900, 1, 1)
        else:
            # 默认返回当前时间
            return current_time

    @staticmethod
    def _format_coupon_data(session: Session, record) -> Dict[str, Any]:
        """格式化优惠券数据

        Args:
            session: 数据库会话
            record: 优惠券使用记录

        Returns:
            Dict[str, Any]: 格式化后的优惠券数据，其中coupon字段根据类型包含对应的特定字段
        """
        # 获取关联的批次信息
        batch = coupon_batch_dao.get(session, record.coupon_batch_id)

        # 根据优惠券类型获取对应的具体对象实例
        coupon_instance = CouponService._get_coupon_instance(session, record.coupon)

        return {
            "coupon_usage_record": {
                "id": record.id,
                "coupon_id": record.coupon_id,
                "coupon_batch_id": record.coupon_batch_id,
                "user_id": record.user_id,
                "order_id": record.order_id,
                "discount_amount": record.discount_amount,
                "used_at": record.used_at,
                "status": record.status.value,
                "created_at": record.created_at,
                "updated_at": record.updated_at
            },
            "coupon": coupon_instance,  # 返回根据类型包含特定字段的字典
            "coupon_batch": {
                "id": batch.id if batch else None,
                "name": batch.name if batch else None,
                "description": batch.description if batch else None,
                "batch_number": batch.batch_number if batch else None,
                "valid_duration": batch.valid_duration if batch else None,
                "start_time": batch.start_time if batch else None,
                "end_time": batch.end_time if batch else None,
                "status": batch.status.value if batch else None,
                "distribution_channels": batch.distribution_channels if batch else None,
                "distribution_quantity": batch.distribution_quantity if batch else None,
                "distribution_cycle": batch.distribution_cycle.value if batch else None,
                "receive_quantity": batch.receive_quantity if batch else None,
                "receive_cycle": batch.receive_cycle.value if batch else None,
                "receive_start_time": batch.receive_start_time if batch else None,
                "receive_end_time": batch.receive_end_time if batch else None
            } if batch else None
        }

    @staticmethod
    def get_user_coupons(session: Session, user: User) -> Dict[str, List[Dict[str, Any]]]:
        """获取用户的优惠券列表（不依赖订单）
        
        Args:
            session: 数据库会话
            user: 用户对象
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 包含三个优惠券列表的字典：
            - available: 在有效期内且可使用的优惠券列表
            - unavailable: 未在有效期内的优惠券列表（包括已过期和未到期的）
            - used: 已经使用的优惠券列表
        """
        try:
            # 1. 获取该用户所有状态为有效的优惠券使用记录（不考虑时间范围）
            valid_records = CouponService._get_all_valid_coupon_records(session, user.id)
            
            # 2. 获取所有该用户状态为已使用的优惠券使用记录
            used_records = CouponService._get_used_coupon_records(session, user.id)
            
            # 3. 初始化三个列表
            available_coupons = []  # 在有效期内且可使用的优惠券
            unavailable_coupons = []    # 未在有效期内的优惠券（过期或未到期）
            used_coupons = []       # 已使用的优惠券
            
            # 4. 处理有效记录，区分有效期内和有效期外
            current_time = datetime.now()
            for record in valid_records:
                coupon_data = CouponService._format_coupon_data(session, record)

                # 检查是否在有效期内（使用批次的时间）
                batch = record.coupon_batch
                if (batch and batch.start_time <= current_time and
                    batch.end_time >= current_time):
                    # 在有效期内，检查使用限制条件
                    is_available = CouponService._check_usage_limit_condition(record, used_records)

                    if is_available:
                        available_coupons.append(coupon_data)
                    else:
                        # 添加不可用原因（仅使用限制）
                        reasons = CouponService._get_usage_limit_reasons(record.coupon)
                        coupon_data["unavailable_reasons"] = reasons
                        available_coupons.append(coupon_data)
                else:
                    # 不在有效期内
                    if batch and batch.end_time < current_time:
                        coupon_data["unavailable_reasons"] = ["优惠券已过期"]
                    else:
                        coupon_data["unavailable_reasons"] = ["优惠券尚未生效"]
                    unavailable_coupons.append(coupon_data)
            
            # 5. 处理已使用的记录
            for record in used_records:
                coupon_data = CouponService._format_coupon_data(session, record)
                used_coupons.append(coupon_data)
            
            result = {
                "available": available_coupons,
                "unavailable": unavailable_coupons,
                "used": used_coupons
            }
            
            logger.info(f"用户 {user.id} 优惠券统计: "
                       f"有效可用 {len(available_coupons)} 张, "
                       f"不可用 {len(unavailable_coupons)} 张, "
                       f"已使用 {len(used_coupons)} 张")
            
            return result
            
        except Exception as e:
            logger.error(f"获取用户优惠券列表失败: {str(e)}")
            raise e

    @staticmethod
    def _get_all_valid_coupon_records(session: Session, user_id: int) -> List:
        """获取用户所有状态为有效的优惠券使用记录（不考虑时间范围）"""
        # 查询有效的优惠券使用记录，并关联优惠券信息
        # 使用 with_polymorphic 确保正确加载多态对象
        records = session.query(coupon_usage_record_dao.model).join(
            coupon_dao.model, coupon_usage_record_dao.model.coupon_id == coupon_dao.model.id
        ).filter(
            and_(
                coupon_usage_record_dao.model.user_id == user_id,
                coupon_usage_record_dao.model.status == CouponUsageStatus.VALID
            )
        ).all()

        return records

    @staticmethod
    def _get_usage_limit_reasons(coupon) -> List[str]:
        """获取使用限制相关的不可用原因
        
        Args:
            coupon: 优惠券对象
            
        Returns:
            List[str]: 不可用原因列表
        """
        reasons = []
        
        if coupon.usage_limit > 0:
            cycle_names = {
                CouponUsageCycle.PER_DAY: "每日",
                CouponUsageCycle.PER_WEEK: "每周", 
                CouponUsageCycle.PER_MONTH: "每月",
                CouponUsageCycle.PER_YEAR: "每年",
                CouponUsageCycle.PER_ORDER: "每次订单"
            }
            cycle_name = cycle_names.get(coupon.usage_cycle, "未知周期")
            reasons.append(f"已达到{cycle_name}使用限制 {coupon.usage_limit} 次")
        
        return reasons

    @staticmethod
    def _get_unavailable_reasons(coupon, order: Order, record, used_records: List) -> List[str]:
        """获取优惠券不可用的原因
        
        Args:
            coupon: 优惠券对象
            order: 订单对象
            session: 数据库会话
            record: 优惠券使用记录
            used_records: 已使用记录列表
            
        Returns:
            List[str]: 不可用原因列表
        """
        reasons = []
        
        # 检查订单金额条件
        if not CouponService._check_order_amount_condition(coupon, order):
            if coupon.condition_scope == CouponScope.ORDER:
                reasons.append(f"订单金额 {order.total_amount} 元不满足最低消费 {coupon.condition_amount} 元的要求")
        
        # 检查产品组合条件
        if not CouponService._check_product_combination_condition(coupon, order):
            if coupon.condition_objects:
                reasons.append("订单中的商品不满足优惠券使用条件")
        
        # 检查使用限制条件
        if not CouponService._check_usage_limit_condition(record, used_records):
            cycle_names = {
                CouponUsageCycle.PER_DAY: "每日",
                CouponUsageCycle.PER_WEEK: "每周", 
                CouponUsageCycle.PER_MONTH: "每月",
                CouponUsageCycle.PER_YEAR: "每年",
                CouponUsageCycle.PER_ORDER: "每次订单"
            }
            cycle_name = cycle_names.get(coupon.usage_cycle, "未知周期")
            reasons.append(f"已达到{cycle_name}使用限制 {coupon.usage_limit} 次")
        
        return reasons

    @staticmethod
    def _get_coupon_instance(session: Session, coupon):
        """根据优惠券类型获取对应的具体对象实例并转换为字典

        Args:
            session: 数据库会话
            coupon: 基础优惠券对象

        Returns:
            对应类型的优惠券数据字典
        """
        # 根据类型获取具体的对象实例
        coupon_instance = None
        coupon_type = coupon.type

        if coupon_type == CouponType.DISCOUNT:
            # 查询折扣券
            coupon_instance = session.query(DiscountCoupon).filter(DiscountCoupon.id == coupon.id).first()
        elif coupon_type == CouponType.CASH:
            # 查询现金券
            coupon_instance = session.query(CashCoupon).filter(CashCoupon.id == coupon.id).first()
        elif coupon_type == CouponType.FULL_REDUCTION:
            # 查询满减券
            coupon_instance = session.query(FullReductionCoupon).filter(FullReductionCoupon.id == coupon.id).first()
        else:
            # 基础优惠券类型
            coupon_instance = coupon

        # 如果没有找到具体实例，使用原始coupon
        if coupon_instance is None:
            coupon_instance = coupon

        # 转换为字典格式
        return CouponService._coupon_to_dict(coupon_instance)

    @staticmethod
    def _coupon_to_dict(coupon_instance) -> Dict[str, Any]:
        """将优惠券对象转换为字典格式

        Args:
            coupon_instance: 优惠券对象实例

        Returns:
            Dict[str, Any]: 优惠券数据字典
        """
        # 基础字段
        base_data = {
            "id": coupon_instance.id,
            "name": coupon_instance.name,
            "description": coupon_instance.description,
            "type": coupon_instance.type.value,
            "condition_scope": coupon_instance.condition_scope.value,
            "condition_objects": coupon_instance.condition_objects,
            "condition_amount": coupon_instance.condition_amount,
            "usage_quantity": coupon_instance.usage_quantity,
            "usage_cycle": coupon_instance.usage_cycle.value,
            "usage_limit": coupon_instance.usage_limit,
            "mutual_exclusive_rules": coupon_instance.mutual_exclusive_rules,
            "payment_channels": coupon_instance.payment_channels,
            "apply_scope": coupon_instance.apply_scope.value,
            "apply_objects": coupon_instance.apply_objects,
            "apply_order": coupon_instance.apply_order,
            "status": coupon_instance.status.value
        }

        # 根据类型添加特定字段
        if isinstance(coupon_instance, DiscountCoupon):
            base_data.update({
                "discount_rate": coupon_instance.discount_rate,
                "min_amount": coupon_instance.min_amount,
                "max_discount": coupon_instance.max_discount
            })
        elif isinstance(coupon_instance, CashCoupon):
            base_data.update({
                "amount": coupon_instance.amount
            })
        elif isinstance(coupon_instance, FullReductionCoupon):
            base_data.update({
                "full_amount": coupon_instance.full_amount,
                "reduction_amount": coupon_instance.reduction_amount
            })

        return base_data






    @staticmethod
    def validate_coupons_for_pricing(
        session: Session,
        user_id: int,
        order_items: List[OrderItemBase],
        coupon_usage_record_ids: List[int]
    ) -> List[Any]:
        """验证优惠券是否可用于计价

        执行7项验证检查：
        1. 检查优惠券使用记录、优惠券、优惠券批次的状态
        2. 检查优惠券批次是否在有效期内
        3. 检查优惠券之间是否存在互斥
        4. 检查订单金额条件
        5. 检查产品条件
        6. 检查使用周期限制
        7. 验证失败时抛出异常

        Args:
            session: 数据库会话
            user_id: 用户ID
            order_items: 订单项列表
            coupon_usage_record_ids: 优惠券使用记录ID列表

        Returns:
            List[Any]: 验证通过的优惠券使用记录列表

        Raises:
            ValueError: 验证失败时抛出异常
        """
        if not coupon_usage_record_ids:
            return []

        # 获取所有优惠券使用记录
        usage_records = []
        for record_id in coupon_usage_record_ids:
            record = coupon_usage_record_dao.get(session, record_id)
            if not record:
                raise ValueError(f"优惠券使用记录不存在: {record_id}")
            usage_records.append(record)

        # 验证每张优惠券
        validated_records = []
        for record in usage_records:
            CouponService._validate_single_coupon(session, user_id, order_items, record, usage_records)
            validated_records.append(record)

        return validated_records

    @staticmethod
    def _validate_single_coupon(
        session: Session,
        user_id: int,
        order_items: List[OrderItemBase],
        record: Any,
        all_records: List[Any]
    ) -> None:
        """验证单张优惠券

        Args:
            session: 数据库会话
            user_id: 用户ID
            order_items: 订单项列表
            record: 当前验证的优惠券使用记录
            all_records: 所有优惠券使用记录列表

        Raises:
            ValueError: 验证失败时抛出异常
        """
        # 1. 检查状态
        CouponService._check_coupon_status(record)

        # 2. 检查有效期
        CouponService._check_coupon_validity_period(record)

        # 3. 检查互斥规则
        CouponService._check_mutual_exclusive_rules(record, all_records)

        # 4. 检查订单金额条件
        CouponService._check_order_amount_condition_for_pricing(session, record.coupon, order_items)

        # 5. 检查产品条件
        CouponService._check_product_condition_for_pricing(session, record.coupon, order_items)

        # 6. 检查使用周期限制
        CouponService._check_usage_cycle_limit(session, user_id, record)

    @staticmethod
    def _check_coupon_status(record: Any) -> None:
        """检查优惠券相关状态

        Args:
            record: 优惠券使用记录

        Raises:
            ValueError: 状态检查失败时抛出异常
        """
        # 检查优惠券使用记录状态
        if record.status != CouponUsageStatus.VALID:
            raise ValueError(f"优惠券使用记录状态无效: {record.status.value}")

        # 检查优惠券状态
        if record.coupon.status != Status.ACTIVE:
            raise ValueError(f"优惠券状态无效: {record.coupon.status.value}")

        # 检查优惠券批次状态
        if record.coupon_batch.status != Status.ACTIVE:
            raise ValueError(f"优惠券批次状态无效: {record.coupon_batch.status.value}")

    @staticmethod
    def _check_coupon_validity_period(record: Any) -> None:
        """检查优惠券批次有效期

        Args:
            record: 优惠券使用记录

        Raises:
            ValueError: 有效期检查失败时抛出异常
        """
        current_time = datetime.now()
        batch = record.coupon_batch

        if current_time < batch.start_time:
            raise ValueError(f"优惠券尚未生效，生效时间: {batch.start_time}")

        if current_time > batch.end_time:
            raise ValueError(f"优惠券已过期，过期时间: {batch.end_time}")

    @staticmethod
    def _check_mutual_exclusive_rules(record: Any, all_records: List[Any]) -> None:
        """检查互斥规则

        Args:
            record: 当前优惠券使用记录
            all_records: 所有已选择的优惠券使用记录列表

        Raises:
            ValueError: 互斥检查失败时抛出异常
        """
        current_coupon = record.coupon
        if not current_coupon.mutual_exclusive_rules:
            return

        # 检查当前优惠券是否与其他已选择的优惠券互斥
        for other_record in all_records:
            if other_record.id == record.id:
                continue

            other_coupon = other_record.coupon

            # 检查其他优惠券的ID是否在当前优惠券的互斥规则中
            if other_coupon.id in current_coupon.mutual_exclusive_rules:
                raise ValueError(f"优惠券 {current_coupon.name} 与 {other_coupon.name} 不能同时使用")

            # 检查当前优惠券的ID是否在其他优惠券的互斥规则中
            if current_coupon.id in other_coupon.mutual_exclusive_rules:
                raise ValueError(f"优惠券 {current_coupon.name} 与 {other_coupon.name} 不能同时使用")

    @staticmethod
    def _check_order_amount_condition_for_pricing(session: Session, coupon: Any, order_items: List[OrderItemBase]) -> None:
        """检查订单金额条件

        Args:
            session: 数据库会话
            coupon: 优惠券对象
            order_items: 订单项列表

        Raises:
            ValueError: 订单金额条件检查失败时抛出异常
        """
        if coupon.condition_scope != CouponScope.ORDER:
            return

        # 计算总金额（使用已经计算过pricing的payable_amount）
        total_amount = sum(item.payable_amount for item in order_items)
        total_amount = round(total_amount, 2)

        if total_amount < coupon.condition_amount:
            raise ValueError(f"订单金额 {total_amount} 元不满足最低消费 {coupon.condition_amount} 元的要求")

    @staticmethod
    def _check_product_condition_for_pricing(session: Session, coupon: Any, order_items: List[OrderItemBase]) -> None:
        """检查产品条件

        Args:
            session: 数据库会话
            coupon: 优惠券对象
            order_items: 订单项列表

        Raises:
            ValueError: 产品条件检查失败时抛出异常
        """
        # 检查 condition_product_types 条件
        if coupon.condition_product_types:
            # 获取订单中所有产品的 meal_type
            order_product_ids = [item.product_id for item in order_items]
            order_products = []
            for product_id in order_product_ids:
                product = product_dao.get(session, product_id)
                if product:
                    order_products.append(product)
            
            order_meal_types = set(product.meal_type.value if hasattr(product.meal_type, 'value') else str(product.meal_type) 
                                  for product in order_products if product.meal_type)
            
            # 检查是否有交集
            required_meal_types = set(coupon.condition_product_types)
            if not order_meal_types.intersection(required_meal_types):
                raise ValueError(f"订单中的产品类型不满足优惠券条件，需要包含以下产品类型之一: {', '.join(required_meal_types)}")

        if coupon.condition_scope != CouponScope.PRODUCT:
            return

        if not coupon.condition_objects:
            return

        # 构建产品数量映射
        product_quantities = {}
        for order_item in order_items:
            product_quantities[order_item.product_id] = product_quantities.get(order_item.product_id, 0) + order_item.quantity

        # 检查condition_objects中的每个条件
        # condition_objects格式: [{"1": 13}, {"5": 2}]
        for condition_item in coupon.condition_objects:
            for product_id_str, required_quantity in condition_item.items():
                product_id = int(product_id_str)
                actual_quantity = product_quantities.get(product_id, 0)

                if actual_quantity < required_quantity:
                    product = product_dao.get(session, product_id)
                    product_name = product.name if product else f"产品{product_id}"
                    raise ValueError(f"产品 {product_name} 数量 {actual_quantity} 不满足条件要求 {required_quantity}")

    @staticmethod
    def _check_usage_cycle_limit(session: Session, user_id: int, record: Any) -> None:
        """检查使用周期限制

        Args:
            session: 数据库会话
            user_id: 用户ID
            record: 优惠券使用记录

        Raises:
            ValueError: 使用周期限制检查失败时抛出异常
        """
        coupon = record.coupon
        if coupon.usage_quantity <= 0:
            return

        # 计算周期开始时间
        current_time = datetime.now()
        cycle_start_time = CouponService._get_cycle_start_time(current_time, coupon.usage_cycle)

        # 查询同类优惠券在周期内的使用次数
        used_count = session.query(coupon_usage_record_dao.model).filter(
            coupon_usage_record_dao.model.user_id == user_id,
            coupon_usage_record_dao.model.coupon_id == coupon.id,
            coupon_usage_record_dao.model.status == CouponUsageStatus.USED,
            coupon_usage_record_dao.model.used_at >= cycle_start_time
        ).count()

        if used_count >= coupon.usage_quantity:
            cycle_name = CouponService._get_cycle_name(coupon.usage_cycle)
            raise ValueError(f"优惠券 {coupon.name} 在{cycle_name}内已达到使用上限 {coupon.usage_quantity} 次")

    @staticmethod
    def _get_cycle_start_time(current_time: datetime, cycle: CouponUsageCycle) -> datetime:
        """获取周期开始时间

        Args:
            current_time: 当前时间
            cycle: 使用周期

        Returns:
            datetime: 周期开始时间
        """
        if cycle == CouponUsageCycle.PER_DAY:
            return current_time.replace(hour=0, minute=0, second=0, microsecond=0)
        elif cycle == CouponUsageCycle.PER_WEEK:
            days_since_monday = current_time.weekday()
            return (current_time - timedelta(days=days_since_monday)).replace(hour=0, minute=0, second=0, microsecond=0)
        elif cycle == CouponUsageCycle.PER_MONTH:
            return current_time.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif cycle == CouponUsageCycle.PER_YEAR:
            return current_time.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        else:  # PER_ORDER
            return current_time

    @staticmethod
    def _get_cycle_name(cycle: CouponUsageCycle) -> str:
        """获取周期名称

        Args:
            cycle: 使用周期

        Returns:
            str: 周期名称
        """
        cycle_names = {
            CouponUsageCycle.PER_ORDER: "每次订单",
            CouponUsageCycle.PER_DAY: "每天",
            CouponUsageCycle.PER_WEEK: "每周",
            CouponUsageCycle.PER_MONTH: "每月",
            CouponUsageCycle.PER_YEAR: "每年"
        }
        return cycle_names.get(cycle, "未知周期")

    @staticmethod
    def calculate_coupon_discounts(
        session: Session,
        order_items: List[OrderItemBase],
        validated_records: List[Any]
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], float]:
        """计算优惠券优惠金额

        Args:
            session: 数据库会话
            order_items: 订单项列表
            validated_records: 已验证的优惠券使用记录列表

        Returns:
            Tuple[List[Dict], List[Dict], float]: (订单项目列表, 优惠券列表, 总优惠金额)
        """
        # 将OrderItemBase转换为计算用的字典格式
        calculated_order_items = CouponService._convert_order_items_to_dict(session, order_items)

        if not validated_records:
            # 没有优惠券，返回原价
            return calculated_order_items, [], 0.0

        # 按照apply_order排序优惠券
        sorted_records = sorted(validated_records, key=lambda x: x.coupon.apply_order)

        # 初始化订单项目
        coupon_discounts = []
        total_discount = 0.0

        # 逐个应用优惠券
        for record in sorted_records:
            coupon = record.coupon
            discount_amount = 0.0

            if coupon.apply_scope == CouponScope.ORDER:
                # 订单级别优惠
                discount_amount = CouponService._apply_order_level_coupon(session, coupon, calculated_order_items)
                if discount_amount > 0:
                    # 按比例分摊到各个订单项
                    CouponService._distribute_order_discount(session, coupon, calculated_order_items, discount_amount)
            else:
                # 产品级别优惠
                discount_amount = CouponService._apply_product_level_coupon(session, coupon, calculated_order_items)

            if discount_amount > 0:
                # 保留两位小数
                discount_amount = round(discount_amount, 2)
                coupon_discounts.append({
                    "coupon_usage_record_id": record.id,
                    "coupon_id": coupon.id,
                    "coupon_name": coupon.name,
                    "quantity": 1,
                    "discount_amount": discount_amount
                })
                total_discount += discount_amount

        # 保留两位小数
        total_discount = round(total_discount, 2)
        return calculated_order_items, coupon_discounts, total_discount

    @staticmethod
    def _convert_order_items_to_dict(session: Session, order_items: List[OrderItemBase]) -> List[Dict[str, Any]]:
        """将OrderItemBase转换为计算用的字典格式

        Args:
            session: 数据库会话
            order_items: 订单项列表

        Returns:
            List[Dict]: 字典格式的订单项列表
        """
        from app.dao.product import product_dao

        calculated_items = []
        for item in order_items:
            # 获取产品信息
            product = product_dao.get(session, item.product_id)
            product_name = product.name if product else f"产品{item.product_id}"

            calculated_items.append({
                "product_id": item.product_id,
                "product_name": product_name,
                "quantity": item.quantity,
                "unit_price": round(item.price, 2),
                "final_unit_price": round(item.final_price, 2),
                "subtotal": round(item.subtotal, 2),
                "payable_amount": round(item.payable_amount, 2),
                "original_payable_amount": round(item.payable_amount, 2)  # 保存原始应付金额用于优惠计算
            })

        return calculated_items

    @staticmethod
    def _calculate_original_order_items(session: Session, products: List[ProductQuantityItem]) -> List[Dict[str, Any]]:
        """计算原始订单项目（不含优惠）

        Args:
            session: 数据库会话
            products: 产品数量组合列表

        Returns:
            List[Dict]: 订单项目列表
        """
        from app.service.pricing import PricingService

        order_items = []
        for product_item in products:
            product = product_dao.get(session, product_item.product_id)
            if not product:
                continue

            # 使用现有的product_pricing方法计算价格
            final_price, payable_amount, remark = PricingService.product_pricing(
                session, product_item.product_id, product.price, product_item.quantity
            )

            order_items.append({
                "product_id": product.id,
                "product_name": product.name,
                "quantity": product_item.quantity,
                "unit_price": round(product.price, 2),
                "final_unit_price": round(final_price, 2),
                "subtotal": round(final_price * product_item.quantity, 2),
                "payable_amount": round(payable_amount, 2),
                "original_payable_amount": round(payable_amount, 2)  # 保存原始应付金额用于优惠计算
            })

        return order_items

    @staticmethod
    def _apply_order_level_coupon(session: Session, coupon: Any, order_items: List[Dict[str, Any]]) -> float:
        """应用订单级别优惠券

        Args:
            session: 数据库会话
            coupon: 优惠券对象
            order_items: 订单项目列表

        Returns:
            float: 优惠金额
        """
        # 如果有 apply_product_types 限制，只计算符合条件的产品金额
        if coupon.apply_product_types:
            eligible_amount = 0.0
            for order_item in order_items:
                product = product_dao.get(session, order_item["product_id"])
                if product and product.meal_type:
                    product_meal_type = product.meal_type.value if hasattr(product.meal_type, 'value') else str(product.meal_type)
                    if product_meal_type in coupon.apply_product_types:
                        eligible_amount += order_item["payable_amount"]
            total_amount = eligible_amount
        else:
            # 计算订单总金额
            total_amount = sum(item["payable_amount"] for item in order_items)

        if coupon.type == CouponType.CASH:
            # 现金券：直接减免
            cash_coupon = coupon  # 假设已经是CashCoupon实例
            return round(min(cash_coupon.amount, total_amount), 2)

        elif coupon.type == CouponType.DISCOUNT:
            # 折扣券：按比例折扣
            discount_coupon = coupon  # 假设已经是DiscountCoupon实例
            if total_amount >= discount_coupon.min_amount:
                discount = total_amount * (1 - discount_coupon.discount_rate)
                if discount_coupon.max_discount > 0:
                    return round(min(discount, discount_coupon.max_discount), 2)
                else:
                    return round(discount, 2)
            return 0.0

        elif coupon.type == CouponType.FULL_REDUCTION:
            # 满减券：满额减价
            full_reduction_coupon = coupon  # 假设已经是FullReductionCoupon实例
            if total_amount >= full_reduction_coupon.full_amount:
                return round(min(full_reduction_coupon.reduction_amount, total_amount), 2)
            return 0.0

        return 0.0

    @staticmethod
    def _apply_product_level_coupon(session: Session, coupon: Any, order_items: List[Dict[str, Any]]) -> float:
        """应用产品级别优惠券

        Args:
            session: 数据库会话
            coupon: 优惠券对象
            order_items: 订单项目列表

        Returns:
            float: 优惠金额
        """
        total_discount = 0.0

        # 如果有 apply_product_types 限制，只对符合条件的产品应用优惠
        if coupon.apply_product_types:
            # 获取所有符合 apply_product_types 条件的产品ID
            eligible_product_ids = set()
            for order_item in order_items:
                product = product_dao.get(session, order_item["product_id"])
                if product and product.meal_type:
                    product_meal_type = product.meal_type.value if hasattr(product.meal_type, 'value') else str(product.meal_type)
                    if product_meal_type in coupon.apply_product_types:
                        eligible_product_ids.add(order_item["product_id"])
            
            # 如果没有符合条件的产品，直接返回0
            if not eligible_product_ids:
                return 0.0
            
            # 只对符合条件的产品应用优惠
            if coupon.apply_objects:
                # apply_objects格式: [{"1": 2}, {"5": 1}] 表示对产品1优惠2个，产品5优惠1个
                for apply_item in coupon.apply_objects:
                    for product_id_str, apply_quantity in apply_item.items():
                        product_id = int(product_id_str)
                        
                        # 检查产品是否符合 apply_product_types 条件
                        if product_id not in eligible_product_ids:
                            continue

                        # 找到对应的订单项
                        for order_item in order_items:
                            if order_item["product_id"] == product_id:
                                discount = CouponService._calculate_product_discount(coupon, order_item, apply_quantity)
                                # 更新订单项的应付金额
                                order_item["payable_amount"] = round(max(0, order_item["payable_amount"] - discount), 2)
                                total_discount += discount
                                break
            else:
                # 如果没有 apply_objects，对所有符合 apply_product_types 的产品应用优惠
                for order_item in order_items:
                    if order_item["product_id"] in eligible_product_ids:
                        discount = CouponService._calculate_product_discount(coupon, order_item, order_item["quantity"])
                        # 更新订单项的应付金额
                        order_item["payable_amount"] = round(max(0, order_item["payable_amount"] - discount), 2)
                        total_discount += discount
        else:
            # 原有逻辑：没有 apply_product_types 限制时
            if not coupon.apply_objects:
                return 0.0

            # apply_objects格式: [{"1": 2}, {"5": 1}] 表示对产品1优惠2个，产品5优惠1个
            for apply_item in coupon.apply_objects:
                for product_id_str, apply_quantity in apply_item.items():
                    product_id = int(product_id_str)

                    # 找到对应的订单项
                    for order_item in order_items:
                        if order_item["product_id"] == product_id:
                            discount = CouponService._calculate_product_discount(coupon, order_item, apply_quantity)
                            # 更新订单项的应付金额
                            order_item["payable_amount"] = round(max(0, order_item["payable_amount"] - discount), 2)
                            total_discount += discount
                            break

        return round(total_discount, 2)

    @staticmethod
    def _calculate_product_discount(coupon: Any, order_item: Dict[str, Any], apply_quantity: int) -> float:
        """计算单个产品的优惠金额

        Args:
            coupon: 优惠券对象
            order_item: 订单项
            apply_quantity: 应用优惠的数量

        Returns:
            float: 优惠金额
        """
        # 计算实际可优惠的数量
        actual_quantity = min(apply_quantity, order_item["quantity"])

        if coupon.type == CouponType.CASH:
            # 现金券：按数量减免
            cash_coupon = coupon
            unit_discount = min(cash_coupon.amount, order_item["final_unit_price"])
            discount = round(unit_discount * actual_quantity, 2)

        elif coupon.type == CouponType.DISCOUNT:
            # 折扣券：按比例折扣
            discount_coupon = coupon
            unit_amount = order_item["final_unit_price"] * actual_quantity
            if unit_amount >= discount_coupon.min_amount:
                unit_discount = unit_amount * (1 - discount_coupon.discount_rate)
                if discount_coupon.max_discount > 0:
                    discount = round(min(unit_discount, discount_coupon.max_discount), 2)
                else:
                    discount = round(unit_discount, 2)
            else:
                discount = 0.0

        elif coupon.type == CouponType.FULL_REDUCTION:
            # 满减券：满额减价
            full_reduction_coupon = coupon
            unit_amount = order_item["final_unit_price"] * actual_quantity
            if unit_amount >= full_reduction_coupon.full_amount:
                discount = round(min(full_reduction_coupon.reduction_amount, unit_amount), 2)
            else:
                discount = 0.0
        else:
            discount = 0.0

        return discount

    @staticmethod
    def _distribute_order_discount(session: Session, coupon: Any, order_items: List[Dict[str, Any]], total_discount: float) -> None:
        """将订单级别优惠按比例分摊到各个订单项

        Args:
            session: 数据库会话
            coupon: 优惠券对象
            order_items: 订单项目列表
            total_discount: 总优惠金额
        """
        # 如果有 apply_product_types 限制，只对符合条件的产品分摊优惠
        eligible_items = []
        if coupon.apply_product_types:
            for order_item in order_items:
                product = product_dao.get(session, order_item["product_id"])
                if product and product.meal_type:
                    product_meal_type = product.meal_type.value if hasattr(product.meal_type, 'value') else str(product.meal_type)
                    if product_meal_type in coupon.apply_product_types:
                        eligible_items.append(order_item)
        else:
            eligible_items = order_items

        if not eligible_items:
            return

        total_amount = sum(item["payable_amount"] for item in eligible_items)
        if total_amount <= 0:
            return

        remaining_discount = total_discount

        for i, item in enumerate(eligible_items):
            if i == len(eligible_items) - 1:
                # 最后一项承担剩余的优惠金额
                item_discount = round(remaining_discount, 2)
            else:
                # 按比例分摊
                item_discount = round((item["payable_amount"] / total_amount) * total_discount, 2)
                remaining_discount -= item_discount

            item["payable_amount"] = round(max(0, item["payable_amount"] - item_discount), 2)

    @staticmethod
    def coupon_pricing(
        session: Session,
        user_id: int,
        order_items: List[OrderItemBase],
        coupon_usage_record_ids: List[int]
    ) -> Dict[str, Any]:
        """基于优惠券的计价核心方法

        Args:
            session: 数据库会话
            user_id: 用户ID
            order_items: 已经计算过pricing的订单项列表
            coupon_usage_record_ids: 优惠券使用记录ID列表

        Returns:
            Dict[str, Any]: 完整的计价结果
        """
        try:
            # 1. 验证优惠券
            validated_records = CouponService.validate_coupons_for_pricing(
                session, user_id, order_items, coupon_usage_record_ids
            )

            # 2. 计算优惠金额
            calculated_order_items, coupon_discounts, total_discount = CouponService.calculate_coupon_discounts(
                session, order_items, validated_records
            )

            # 3. 计算订单总金额
            total_amount = round(sum(item["subtotal"] for item in calculated_order_items), 2)
            payable_amount = round(sum(item["payable_amount"] for item in calculated_order_items), 2)

            # 4. 获取用户的所有优惠券列表（用于返回可用/不可用列表）
            coupon_lists = CouponService._get_user_coupon_lists(session, user_id, coupon_usage_record_ids, order_items)

            # 4.1. 当应付金额小于或等于0时，将未被选中的有效优惠券移动到不可用列表中
            if payable_amount <= 0:
                # 获取未被选中的有效优惠券
                unselected_available_coupons = []
                remaining_available_coupons = []

                for coupon_item in coupon_lists["available_coupons"]:
                    if coupon_item["coupon_usage_record_id"] not in coupon_usage_record_ids:
                        # 未被选中的优惠券，添加不可用原因并移动到不可用列表
                        coupon_item["unavailable_reason"] = "订单金额已为0，无需使用更多优惠券"
                        unselected_available_coupons.append(coupon_item)
                    else:
                        # 已被选中的优惠券，保留在可用列表中
                        remaining_available_coupons.append(coupon_item)

                # 更新优惠券列表分类
                coupon_lists["available_coupons"] = remaining_available_coupons
                coupon_lists["unavailable_coupons"].extend(unselected_available_coupons)

            # 5. 构建返回结果
            result = {
                "pricing_result": {
                    "order": {
                        "total_amount": total_amount,
                        "payable_amount": payable_amount,
                        "order_items": calculated_order_items
                    },
                    "discount": {
                        "total_discount": total_discount,
                        "coupons": coupon_discounts
                    }
                },
                "coupon_lists": coupon_lists
            }

            return result

        except ValueError as e:
            # 验证失败，返回错误信息
            raise e
        except Exception as e:
            logger.error(f"优惠券计价失败: {str(e)}")
            raise ValueError(f"计价失败: {str(e)}")

    @staticmethod
    def _get_user_coupon_lists(
        session: Session,
        user_id: int,
        selected_record_ids: List[int],
        order_items: List[OrderItemBase]
    ) -> Dict[str, List[Dict]]:
        """获取用户优惠券列表

        Args:
            session: 数据库会话
            user_id: 用户ID
            selected_record_ids: 用户已选择的优惠券记录ID列表
            order_items: 订单项列表

        Returns:
            Dict[str, List[Dict]]: 优惠券列表分类
        """
        # 获取用户所有优惠券使用记录（包括valid和used状态）
        all_records = session.query(coupon_usage_record_dao.model).filter(
            coupon_usage_record_dao.model.user_id == user_id,
            coupon_usage_record_dao.model.status.in_([CouponUsageStatus.VALID, CouponUsageStatus.USED])
        ).all()

        used_coupons = []
        available_coupons = []
        unavailable_coupons = []

        # 获取已选择的优惠券ID列表（用于互斥检查）
        selected_coupon_ids = []
        for record_id in selected_record_ids:
            record = coupon_usage_record_dao.get(session, record_id)
            if record:
                selected_coupon_ids.append(record.coupon.id)

        for record in all_records:
            # 格式化有效期
            batch = record.coupon_batch
            start_time = batch.start_time.strftime('%Y-%m-%d %H:%M:%S') if batch.start_time else ''
            end_time = batch.end_time.strftime('%Y-%m-%d %H:%M:%S') if batch.end_time else ''

            coupon_item = {
                "coupon_usage_record_id": record.id,
                "coupon_id": record.coupon.id,
                "coupon_name": record.coupon.name,
                "coupon_description": record.coupon.description or '',
                "coupon_type": record.coupon.type.value,
                "status": record.status.value,
                "valid_start_time": start_time,
                "valid_end_time": end_time,
                "used_at": record.used_at.strftime('%Y-%m-%d %H:%M:%S') if record.used_at else ''
            }

            # 1. 已使用的优惠券：status == "used"
            if record.status == CouponUsageStatus.USED:
                used_coupons.append(coupon_item)
                continue

            # 2. 对于status == "valid"的优惠券，进行可用性检查
            if record.status == CouponUsageStatus.VALID:
                unavailable_reasons = []

                # 检查是否在有效期内
                current_time = datetime.now()
                batch = record.coupon_batch
                if not (batch.start_time <= current_time <= batch.end_time):
                    if current_time < batch.start_time:
                        unavailable_reasons.append("优惠券尚未生效")
                    else:
                        unavailable_reasons.append("优惠券已过期")

                # 检查是否与已选优惠券互斥（只有当前优惠券不在已选列表中时才检查）
                if record.id not in selected_record_ids:
                    # 检查当前优惠券是否与已选优惠券互斥
                    is_mutual_exclusive = False
                    if record.coupon.mutual_exclusive_rules:
                        for selected_coupon_id in selected_coupon_ids:
                            if selected_coupon_id in record.coupon.mutual_exclusive_rules:
                                unavailable_reasons.append(f"与已选优惠券互斥")
                                is_mutual_exclusive = True
                                break

                    # 检查已选优惠券是否与当前优惠券互斥
                    if not is_mutual_exclusive:
                        for selected_record_id in selected_record_ids:
                            selected_record = coupon_usage_record_dao.get(session, selected_record_id)
                            if (selected_record and selected_record.coupon.mutual_exclusive_rules and
                                record.coupon.id in selected_record.coupon.mutual_exclusive_rules):
                                unavailable_reasons.append(f"与已选优惠券互斥")
                                break

                # 检查使用周期限制
                if record.coupon.usage_quantity > 0:
                    cycle_start_time = CouponService._get_cycle_start_time(current_time, record.coupon.usage_cycle)
                    used_count = session.query(coupon_usage_record_dao.model).filter(
                        coupon_usage_record_dao.model.user_id == user_id,
                        coupon_usage_record_dao.model.coupon_id == record.coupon.id,
                        coupon_usage_record_dao.model.status == CouponUsageStatus.USED,
                        coupon_usage_record_dao.model.used_at >= cycle_start_time
                    ).count()

                    if used_count >= record.coupon.usage_quantity:
                        cycle_name = CouponService._get_cycle_name(record.coupon.usage_cycle)
                        unavailable_reasons.append(f"已达到{cycle_name}使用上限")

                # 检查产品条件
                try:
                    # 检查订单金额条件
                    if record.coupon.condition_scope == CouponScope.ORDER and record.coupon.condition_amount > 0:
                        total_amount = sum(item.payable_amount for item in order_items)
                        total_amount = round(total_amount, 2)

                        if total_amount < record.coupon.condition_amount:
                            unavailable_reasons.append(f"订单金额不满足最低消费要求")

                    # 检查产品组合条件
                    if record.coupon.condition_scope == CouponScope.PRODUCT and record.coupon.condition_objects:
                        product_quantities = {}
                        for order_item in order_items:
                            product_quantities[order_item.product_id] = product_quantities.get(order_item.product_id, 0) + order_item.quantity

                        condition_met = True
                        for condition_item in record.coupon.condition_objects:
                            for product_id_str, required_quantity in condition_item.items():
                                product_id = int(product_id_str)
                                actual_quantity = product_quantities.get(product_id, 0)
                                if actual_quantity < required_quantity:
                                    condition_met = False
                                    break
                            if not condition_met:
                                break

                        if not condition_met:
                            unavailable_reasons.append("产品组合不满足使用条件")

                except Exception:
                    unavailable_reasons.append("条件检查异常")

                # 根据检查结果分类
                if unavailable_reasons:
                    coupon_item["unavailable_reason"] = "; ".join(unavailable_reasons)
                    unavailable_coupons.append(coupon_item)
                else:
                    available_coupons.append(coupon_item)

        return {
            "used_coupons": used_coupons,
            "unavailable_coupons": unavailable_coupons,
            "available_coupons": available_coupons
        }


# 创建服务实例
coupon_service = CouponService()
